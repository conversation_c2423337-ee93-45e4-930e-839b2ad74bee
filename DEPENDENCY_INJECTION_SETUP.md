# Dependency Injection Setup Guide

## ✅ Setup Complete

The DataRouter package now includes a complete dependency injection setup similar to the auth package structure, using `injectable` and `get_it` for clean, maintainable dependency management.

## 🏗️ Architecture Overview

### **Dependency Injection Structure**
```
lib/src/core/
├── di/
│   ├── di.dart              # Main DI configuration
│   ├── di.config.dart       # Generated DI configuration
│   └── database_module.dart # Database module
├── config/
│   └── config.dart          # Configuration management
└── database.dart            # Database class (no DI annotations)
```

## 📋 Implementation Details

### **1. Main DI Configuration (`lib/src/core/di/di.dart`)**

```dart
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

/// Global GetIt instance for dependency injection
final GetIt getIt = GetIt.instance;

/// Configure dependency injection
@InjectableInit()
Future<void> configureInjection() async {
  await getIt.init();
}

/// Reset dependency injection (useful for testing)
Future<void> resetInjection() async {
  await getIt.reset();
}

/// Check if dependency injection is configured
bool get isInjectionConfigured => getIt.isRegistered<DataRouterDatabase>();
```

### **2. Database Module (`lib/src/core/di/database_module.dart`)**

```dart
@module
abstract class DatabaseModule {
  /// Provide ObjectBox Store instance
  @LazySingleton()
  @preResolve
  Future<Store> provideObjectBoxStore() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final storeDir = Directory('${docsDir.path}/data_router_db');
    
    if (!storeDir.existsSync()) {
      storeDir.createSync(recursive: true);
    }
    
    return Store(getObjectBoxModel(), directory: storeDir.path);
  }

  /// Provide DataRouterDatabase instance
  @LazySingleton()
  DataRouterDatabase provideDataRouterDatabase(Store store) {
    return DataRouterDatabase(store);
  }
}
```

### **3. Configuration Class (`lib/src/core/config/config.dart`)**

```dart
@LazySingleton()
class DataRouterConfig {
  bool _isInitialized = false;
  
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    await configureInjection();
    _isInitialized = true;
  }

  Future<void> reset() async {
    await resetInjection();
    _isInitialized = false;
  }

  static DataRouterConfig get instance => getIt<DataRouterConfig>();
}
```

### **4. Updated Database Class (`lib/src/core/database.dart`)**

```dart
/// Main database class for DataRouter
/// Manages ObjectBox store and provides database operations
class DataRouterDatabase {
  final Store _store;
  Admin? _admin;

  /// Constructor for dependency injection
  DataRouterDatabase(this._store) {
    _initializeAdmin();
  }

  /// Get the ObjectBox store instance
  Store get store => _store;

  /// Check if database is initialized
  bool get isInitialized => true; // Always true since store is injected

  /// Get the ObjectBox Admin instance (only available in debug mode)
  Admin? get admin => _admin;
  
  // ... rest of the methods
}
```

## 🚀 Usage Examples

### **1. Basic Setup in main.dart**

```dart
import 'package:flutter/material.dart';
import 'package:data_router/data_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize dependency injection
  await configureInjection();
  
  runApp(const MyApp());
}
```

### **2. Using Database in Widgets**

```dart
class DatabaseDemoTab extends StatefulWidget {
  @override
  State<DatabaseDemoTab> createState() => _DatabaseDemoTabState();
}

class _DatabaseDemoTabState extends State<DatabaseDemoTab> {
  // Get database instance from DI
  late final DataRouterDatabase _database;

  @override
  void initState() {
    super.initState();
    // Get database instance from dependency injection
    _database = getIt<DataRouterDatabase>();
    _initializeDatabase();
  }

  @override
  void dispose() {
    _database.closeAdmin();
    super.dispose();
  }
  
  // ... rest of the implementation
}
```

### **3. Using Database in Use Cases**

```dart
@LazySingleton()
class GetSessionsUseCase {
  final DataRouterDatabase _database;
  
  GetSessionsUseCase(this._database);
  
  Future<Result<List<SessionEntity>>> execute() async {
    try {
      final sessionBox = _database.store.box<SessionEntity>();
      final sessions = sessionBox.getAll();
      return Success(sessions);
    } catch (e) {
      return Failure('Failed to get sessions: $e');
    }
  }
}
```

### **4. Testing with DI**

```dart
void main() {
  group('Database Tests', () {
    setUp(() async {
      await configureInjection();
    });

    tearDown(() async {
      await resetInjection();
    });

    test('should get database instance', () {
      final database = getIt<DataRouterDatabase>();
      expect(database, isNotNull);
      expect(database.isInitialized, isTrue);
    });
  });
}
```

## 🔧 Key Benefits

### **1. Clean Architecture**
- ✅ **Separation of Concerns** - Database creation logic separated from business logic
- ✅ **Dependency Inversion** - High-level modules don't depend on low-level modules
- ✅ **Single Responsibility** - Each class has one reason to change

### **2. Testability**
- ✅ **Easy Mocking** - Dependencies can be easily mocked for testing
- ✅ **Isolated Testing** - Each component can be tested in isolation
- ✅ **Setup/Teardown** - Clean test environment setup and cleanup

### **3. Maintainability**
- ✅ **Centralized Configuration** - All DI setup in one place
- ✅ **Type Safety** - Compile-time dependency resolution
- ✅ **Auto-generated Code** - Reduces boilerplate and errors

### **4. Performance**
- ✅ **Lazy Loading** - Dependencies created only when needed
- ✅ **Singleton Management** - Automatic singleton lifecycle management
- ✅ **Async Support** - Proper handling of async initialization

## 📦 Dependencies Added

```yaml
dependencies:
  # Dependency injection
  injectable: ^2.1.0
  get_it: ^7.6.0
  
  # Path utilities
  path_provider: ^2.1.1

dev_dependencies:
  # Code generation
  injectable_generator: ^2.4.1
  build_runner: ^2.4.0
```

## 🔄 Code Generation

To generate the DI configuration:

```bash
# Generate DI configuration
dart run build_runner build --delete-conflicting-outputs

# Watch for changes (development)
dart run build_runner watch --delete-conflicting-outputs
```

## 🎯 Migration from Direct Instantiation

### **Before (Direct Instantiation)**
```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  final DataRouterDatabase _database = DataRouterDatabase();

  @override
  void initState() {
    super.initState();
    _database.initialize();
  }
}
```

### **After (Dependency Injection)**
```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late final DataRouterDatabase _database;

  @override
  void initState() {
    super.initState();
    _database = getIt<DataRouterDatabase>();
    // Database is already initialized through DI
  }
}
```

## 🧪 Testing Strategy

### **Unit Tests**
```dart
void main() {
  group('Database Unit Tests', () {
    late DataRouterDatabase database;
    late Store mockStore;

    setUp(() {
      mockStore = MockStore();
      database = DataRouterDatabase(mockStore);
    });

    test('should return store instance', () {
      expect(database.store, equals(mockStore));
    });
  });
}
```

### **Integration Tests**
```dart
void main() {
  group('Database Integration Tests', () {
    setUp(() async {
      await configureInjection();
    });

    tearDown(() async {
      await resetInjection();
    });

    test('should perform CRUD operations', () async {
      final database = getIt<DataRouterDatabase>();
      // Test actual database operations
    });
  });
}
```

## 🎉 Summary

The dependency injection setup provides:

- ✅ **Clean Architecture** following SOLID principles
- ✅ **Easy Testing** with proper mocking capabilities
- ✅ **Maintainable Code** with centralized dependency management
- ✅ **Type Safety** with compile-time dependency resolution
- ✅ **Performance** with lazy loading and singleton management
- ✅ **ObjectBox Admin** integration with proper lifecycle management

The setup follows the same patterns used in the auth package, ensuring consistency across the codebase! 🚀
