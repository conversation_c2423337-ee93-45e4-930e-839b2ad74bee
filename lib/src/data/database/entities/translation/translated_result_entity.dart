import 'package:objectbox/objectbox.dart';

/// Translated result entity for storing translation results
/// UID range: 3400-3409
@Entity()
class TranslatedResultEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3400)
  String workspaceId = '';

  /// Channel ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3401)
  String channelId = '';

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3402)
  String sessionKey = '';

  /// Message ID (indexed, FIXED, UID UPDATED)
  @Index()
  @Property(uid: 3403)
  String messageId = '';

  /// Original content (UID UPDATED)
  @Property(uid: 3404, type: PropertyType.string)
  String originalContent = '';

  /// Translated content (UID UPDATED)
  @Property(uid: 3405, type: PropertyType.string)
  String translatedContent = '';

  /// Original language (UID UPDATED)
  @Property(uid: 3406)
  String originalLanguage = '';

  /// Target language (UID UPDATED)
  @Property(uid: 3407)
  String targetLanguage = '';

  /// Translation status (UID UPDATED)
  @Property(uid: 3408)
  int statusRaw = 0;

  /// Whether to show translate result (UID UPDATED)
  @Property(uid: 3409)
  bool isShowTranslateResult = false;

  /// Composite index field: messageIdLanguage
  String get messageIdLanguage => '${messageId}_${targetLanguage}';

  /// Default constructor
  TranslatedResultEntity();

  /// Constructor with required fields
  TranslatedResultEntity.create({
    required this.workspaceId,
    required this.channelId,
    required this.sessionKey,
    required this.messageId,
    this.originalContent = '',
    this.translatedContent = '',
    this.originalLanguage = '',
    this.targetLanguage = '',
    this.statusRaw = 0,
    this.isShowTranslateResult = false,
  });

  /// Copy constructor for updates
  TranslatedResultEntity copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey,
    String? messageId,
    String? originalContent,
    String? translatedContent,
    String? originalLanguage,
    String? targetLanguage,
    int? statusRaw,
    bool? isShowTranslateResult,
  }) {
    return TranslatedResultEntity()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..sessionKey = sessionKey ?? this.sessionKey
      ..messageId = messageId ?? this.messageId
      ..originalContent = originalContent ?? this.originalContent
      ..translatedContent = translatedContent ?? this.translatedContent
      ..originalLanguage = originalLanguage ?? this.originalLanguage
      ..targetLanguage = targetLanguage ?? this.targetLanguage
      ..statusRaw = statusRaw ?? this.statusRaw
      ..isShowTranslateResult = isShowTranslateResult ?? this.isShowTranslateResult;
  }

  /// Update translation result
  void updateTranslation({
    String? translatedContent,
    int? statusRaw,
  }) {
    if (translatedContent != null) this.translatedContent = translatedContent;
    if (statusRaw != null) this.statusRaw = statusRaw;
  }

  /// Show translation result
  void showTranslateResult() {
    isShowTranslateResult = true;
  }

  /// Hide translation result
  void hideTranslateResult() {
    isShowTranslateResult = false;
  }

  /// Check if translation is completed
  bool get isCompleted => statusRaw == 1; // Assuming 1 means completed

  /// Check if translation is in progress
  bool get isInProgress => statusRaw == 0; // Assuming 0 means in progress

  /// Check if translation failed
  bool get isFailed => statusRaw == -1; // Assuming -1 means failed

  /// Check if translation has content
  bool get hasTranslation => translatedContent.isNotEmpty;

  @override
  String toString() {
    return 'TranslatedResultEntity{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'sessionKey: $sessionKey, '
        'messageId: $messageId, '
        'originalLanguage: $originalLanguage, '
        'targetLanguage: $targetLanguage, '
        'statusRaw: $statusRaw, '
        'isShowTranslateResult: $isShowTranslateResult, '
        'hasTranslation: $hasTranslation'
        '}';
  }
}
