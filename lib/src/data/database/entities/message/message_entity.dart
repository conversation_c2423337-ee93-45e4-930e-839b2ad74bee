import 'package:objectbox/objectbox.dart';

/// Message entity for storing chat messages
/// UID range: 3200-3230
@Entity()
class MessageEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3200)
  String workspaceId = '';

  /// Channel ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3201)
  String channelId = '';

  /// Unique message ID (indexed, UID UPDATED)
  @Unique()
  @Index()
  @Property(uid: 3202)
  String messageId = '';

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3203)
  String sessionKey = '';

  /// User ID who sent the message (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3204)
  String userId = '';

  /// Message view type (UID UPDATED)
  @Property(uid: 3205)
  int messageViewTypeRaw = 0;

  /// Content arguments (UID UPDATED)
  @Property(uid: 3206, type: PropertyType.string)
  String contentArguments = '';

  /// Message content (UID UPDATED)
  @Property(uid: 3207, type: PropertyType.string)
  String content = '';

  /// Message reference (UID UPDATED)
  @Property(uid: 3208)
  String ref = '';

  /// Content locale (UID UPDATED)
  @Property(uid: 3209)
  String contentLocale = '';

  /// Message type (UID UPDATED)
  @Property(uid: 3210)
  int messageTypeRaw = 0;

  /// Message status (UID UPDATED)
  @Property(uid: 3211)
  int messageStatusRaw = 0;

  /// Message error reason (UID UPDATED)
  @Property(uid: 3212)
  int messageErrorReasonRaw = 0;

  /// Attachment type (UID UPDATED)
  @Property(uid: 3213)
  int attachmentTypeRaw = 0;

  /// Whether message is a thread (UID UPDATED)
  @Property(uid: 3214)
  bool isThread = false;

  /// Report count (UID UPDATED)
  @Property(uid: 3215)
  int reportCount = 0;

  /// Whether message is reported (UID UPDATED)
  @Property(uid: 3216)
  bool isReported = false;

  /// Attachment count (UID UPDATED)
  @Property(uid: 3217)
  int attachmentCount = 0;

  /// Message creation time (UID UPDATED)
  @Property(uid: 3218)
  DateTime? createTime;

  /// Message last update time (UID UPDATED)
  @Property(uid: 3219)
  DateTime? updateTime;

  /// Whether this is the first message in channel (UID UPDATED)
  @Property(uid: 3220)
  bool isFirstMessage = false;

  /// Original message raw data (UID UPDATED)
  @Property(uid: 3221, type: PropertyType.string)
  String originalMessageRaw = '';

  /// Reactions raw data (UID UPDATED)
  @Property(uid: 3222, type: PropertyType.string)
  String reactionsRaw = '';

  /// Mentions raw data (UID UPDATED)
  @Property(uid: 3223, type: PropertyType.string)
  String mentionsRaw = '';

  /// Embed raw data (UID UPDATED)
  @Property(uid: 3224, type: PropertyType.string)
  String embedRaw = '';

  /// Data mentions raw (UID UPDATED)
  @Property(uid: 3225, type: PropertyType.string)
  String dataMentionsRaw = '';

  /// Message edit time (UID UPDATED)
  @Property(uid: 3226)
  DateTime? editTime;

  /// Whether this is a partial message record (UID UPDATED)
  @Property(uid: 3227)
  bool isPartial = false;

  /// Whether this is a temporary message (UID UPDATED)
  @Property(uid: 3228)
  bool isTemp = false;

  /// Whether message is pinned (UID UPDATED)
  @Property(uid: 3229)
  bool isPinned = false;

  /// Pin time (UID UPDATED)
  @Property(uid: 3230)
  DateTime? pinTime;

  /// Composite index field: channelIdCreateTime
  String get channelIdCreateTime => '${channelId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Composite index field: userIdChannel
  String get userIdChannel => '${userId}_$channelId';

  /// Composite index field: channelIdStatus
  String get channelIdStatus => '${channelId}_$messageStatusRaw';

  /// Composite index field: sessionChannelTime
  String get sessionChannelTime => '${sessionKey}_${channelId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  MessageEntity();

  /// Constructor with required fields
  MessageEntity.create({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.sessionKey,
    required this.userId,
    this.messageViewTypeRaw = 0,
    this.contentArguments = '',
    this.content = '',
    this.ref = '',
    this.contentLocale = '',
    this.messageTypeRaw = 0,
    this.messageStatusRaw = 0,
    this.messageErrorReasonRaw = 0,
    this.attachmentTypeRaw = 0,
    this.isThread = false,
    this.reportCount = 0,
    this.isReported = false,
    this.attachmentCount = 0,
    this.createTime,
    this.updateTime,
    this.isFirstMessage = false,
    this.originalMessageRaw = '',
    this.reactionsRaw = '',
    this.mentionsRaw = '',
    this.embedRaw = '',
    this.dataMentionsRaw = '',
    this.editTime,
    this.isPartial = false,
    this.isTemp = false,
    this.isPinned = false,
    this.pinTime,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Pin the message
  void pin() {
    isPinned = true;
    pinTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Unpin the message
  void unpin() {
    isPinned = false;
    pinTime = null;
    updateTime = DateTime.now();
  }

  /// Edit the message
  void edit(String newContent) {
    content = newContent;
    editTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Report the message
  void report() {
    isReported = true;
    reportCount++;
    updateTime = DateTime.now();
  }

  /// Mark as complete message record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Mark as temporary
  void markAsTemp() {
    isTemp = true;
    updateTime = DateTime.now();
  }

  /// Mark as permanent
  void markAsPermanent() {
    isTemp = false;
    updateTime = DateTime.now();
  }

  /// Check if message has content
  bool get hasContent => content.isNotEmpty;

  /// Check if message has attachments
  bool get hasAttachments => attachmentCount > 0;

  /// Check if message is edited
  bool get isEdited => editTime != null;

  /// Check if message has reactions
  bool get hasReactions => reactionsRaw.isNotEmpty;

  /// Check if message has mentions
  bool get hasMentions => mentionsRaw.isNotEmpty;

  @override
  String toString() {
    return 'MessageEntity{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'messageId: $messageId, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'content: ${content.length > 50 ? content.substring(0, 50) + '...' : content}, '
        'messageTypeRaw: $messageTypeRaw, '
        'messageStatusRaw: $messageStatusRaw, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isPartial: $isPartial, '
        'isTemp: $isTemp, '
        'isPinned: $isPinned'
        '}';
  }
}
