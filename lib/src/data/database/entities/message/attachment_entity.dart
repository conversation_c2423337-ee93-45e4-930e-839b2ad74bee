import 'package:objectbox/objectbox.dart';

/// Attachment entity for storing message attachments
/// UID range: 3300-3313
@Entity()
class AttachmentEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Attachment ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 3300)
  String attachmentId = '';

  /// Attachment reference (UID UPDATED)
  @Property(uid: 3301)
  String ref = '';

  /// Sticker raw data (UID UPDATED)
  @Property(uid: 3302, type: PropertyType.string)
  String stickerRaw = '';

  /// Photo raw data (UID UPDATED)
  @Property(uid: 3303, type: PropertyType.string)
  String photoRaw = '';

  /// Audio raw data (UID UPDATED)
  @Property(uid: 3304, type: PropertyType.string)
  String audioRaw = '';

  /// Video raw data (UID UPDATED)
  @Property(uid: 3305, type: PropertyType.string)
  String videoRaw = '';

  /// Voice message raw data (UID UPDATED)
  @Property(uid: 3306, type: PropertyType.string)
  String voiceMessageRaw = '';

  /// Video message raw data (UID UPDATED)
  @Property(uid: 3307, type: PropertyType.string)
  String videoMessageRaw = '';

  /// Media message raw data (UID UPDATED)
  @Property(uid: 3308, type: PropertyType.string)
  String mediaMessageRaw = '';

  /// File message raw data (UID UPDATED)
  @Property(uid: 3309, type: PropertyType.string)
  String fileMessageRaw = '';

  /// Undefined message raw data (UID UPDATED)
  @Property(uid: 3310, type: PropertyType.string)
  String undefinedMessageRaw = '';

  /// Whether this is a temporary attachment (UID UPDATED)
  @Property(uid: 3311)
  bool isTemp = false;

  /// Attachment status (UID UPDATED)
  @Property(uid: 3312)
  int attachmentStatusRaw = 0;

  /// Whether this is a partial attachment record (UID UPDATED)
  @Property(uid: 3313)
  bool isPartial = false;

  /// Default constructor
  AttachmentEntity();

  /// Constructor with required fields
  AttachmentEntity.create({
    required this.attachmentId,
    this.ref = '',
    this.stickerRaw = '',
    this.photoRaw = '',
    this.audioRaw = '',
    this.videoRaw = '',
    this.voiceMessageRaw = '',
    this.videoMessageRaw = '',
    this.mediaMessageRaw = '',
    this.fileMessageRaw = '',
    this.undefinedMessageRaw = '',
    this.isTemp = false,
    this.attachmentStatusRaw = 0,
    this.isPartial = false,
  });

  /// Copy constructor for updates
  AttachmentEntity copyWith({
    int? id,
    String? attachmentId,
    String? ref,
    String? stickerRaw,
    String? photoRaw,
    String? audioRaw,
    String? videoRaw,
    String? voiceMessageRaw,
    String? videoMessageRaw,
    String? mediaMessageRaw,
    String? fileMessageRaw,
    String? undefinedMessageRaw,
    bool? isTemp,
    int? attachmentStatusRaw,
    bool? isPartial,
  }) {
    return AttachmentEntity()
      ..id = id ?? this.id
      ..attachmentId = attachmentId ?? this.attachmentId
      ..ref = ref ?? this.ref
      ..stickerRaw = stickerRaw ?? this.stickerRaw
      ..photoRaw = photoRaw ?? this.photoRaw
      ..audioRaw = audioRaw ?? this.audioRaw
      ..videoRaw = videoRaw ?? this.videoRaw
      ..voiceMessageRaw = voiceMessageRaw ?? this.voiceMessageRaw
      ..videoMessageRaw = videoMessageRaw ?? this.videoMessageRaw
      ..mediaMessageRaw = mediaMessageRaw ?? this.mediaMessageRaw
      ..fileMessageRaw = fileMessageRaw ?? this.fileMessageRaw
      ..undefinedMessageRaw = undefinedMessageRaw ?? this.undefinedMessageRaw
      ..isTemp = isTemp ?? this.isTemp
      ..attachmentStatusRaw = attachmentStatusRaw ?? this.attachmentStatusRaw
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Mark as complete attachment record
  void markAsComplete() {
    isPartial = false;
  }

  /// Mark as temporary
  void markAsTemp() {
    isTemp = true;
  }

  /// Mark as permanent
  void markAsPermanent() {
    isTemp = false;
  }

  /// Check if attachment is a sticker
  bool get isSticker => stickerRaw.isNotEmpty;

  /// Check if attachment is a photo
  bool get isPhoto => photoRaw.isNotEmpty;

  /// Check if attachment is audio
  bool get isAudio => audioRaw.isNotEmpty;

  /// Check if attachment is video
  bool get isVideo => videoRaw.isNotEmpty;

  /// Check if attachment is voice message
  bool get isVoiceMessage => voiceMessageRaw.isNotEmpty;

  /// Check if attachment is video message
  bool get isVideoMessage => videoMessageRaw.isNotEmpty;

  /// Check if attachment is media message
  bool get isMediaMessage => mediaMessageRaw.isNotEmpty;

  /// Check if attachment is file message
  bool get isFileMessage => fileMessageRaw.isNotEmpty;

  /// Get attachment type as string
  String get attachmentType {
    if (isSticker) return 'sticker';
    if (isPhoto) return 'photo';
    if (isAudio) return 'audio';
    if (isVideo) return 'video';
    if (isVoiceMessage) return 'voice';
    if (isVideoMessage) return 'video_message';
    if (isMediaMessage) return 'media';
    if (isFileMessage) return 'file';
    return 'undefined';
  }

  @override
  String toString() {
    return 'AttachmentEntity{'
        'id: $id, '
        'attachmentId: $attachmentId, '
        'ref: $ref, '
        'type: $attachmentType, '
        'attachmentStatusRaw: $attachmentStatusRaw, '
        'isTemp: $isTemp, '
        'isPartial: $isPartial'
        '}';
  }
}
