import 'package:objectbox/objectbox.dart';

/// Profile entity for storing user profile information
/// UID range: 2100-2108
@Entity()
class ProfileEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 2100)
  String sessionKey = '';

  /// User ID reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 2101)
  String userId = '';

  /// Avatar URL (UID UPDATED)
  @Property(uid: 2102)
  String avatar = '';

  /// Cover image URL (UID UPDATED)
  @Property(uid: 2103)
  String cover = '';

  /// Display name (UID UPDATED)
  @Property(uid: 2104)
  String displayName = '';

  /// Original avatar URL (UID UPDATED)
  @Property(uid: 2105)
  String originalAvatar = '';

  /// Phone number (UID UPDATED)
  @Property(uid: 2106)
  String phoneNumber = '';

  /// Email address (UID UPDATED)
  @Property(uid: 2107)
  String email = '';

  /// User badge type (UID UPDATED)
  @Property(uid: 2108)
  int userBadgeType = 0;

  /// Composite index field: sessionUserId
  String get sessionUserId => '${sessionKey}_$userId';

  /// Default constructor
  ProfileEntity();

  /// Constructor with required fields
  ProfileEntity.create({
    required this.sessionKey,
    required this.userId,
    this.avatar = '',
    this.cover = '',
    this.displayName = '',
    this.originalAvatar = '',
    this.phoneNumber = '',
    this.email = '',
    this.userBadgeType = 0,
  });

  /// Copy constructor for updates
  ProfileEntity copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? avatar,
    String? cover,
    String? displayName,
    String? originalAvatar,
    String? phoneNumber,
    String? email,
    int? userBadgeType,
  }) {
    return ProfileEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..avatar = avatar ?? this.avatar
      ..cover = cover ?? this.cover
      ..displayName = displayName ?? this.displayName
      ..originalAvatar = originalAvatar ?? this.originalAvatar
      ..phoneNumber = phoneNumber ?? this.phoneNumber
      ..email = email ?? this.email
      ..userBadgeType = userBadgeType ?? this.userBadgeType;
  }

  /// Update profile information
  void updateProfile({
    String? avatar,
    String? cover,
    String? displayName,
    String? originalAvatar,
    String? phoneNumber,
    String? email,
    int? userBadgeType,
  }) {
    if (avatar != null) this.avatar = avatar;
    if (cover != null) this.cover = cover;
    if (displayName != null) this.displayName = displayName;
    if (originalAvatar != null) this.originalAvatar = originalAvatar;
    if (phoneNumber != null) this.phoneNumber = phoneNumber;
    if (email != null) this.email = email;
    if (userBadgeType != null) this.userBadgeType = userBadgeType;
  }

  /// Check if profile has avatar
  bool get hasAvatar => avatar.isNotEmpty;

  /// Check if profile has cover
  bool get hasCover => cover.isNotEmpty;

  /// Check if profile has display name
  bool get hasDisplayName => displayName.isNotEmpty;

  /// Get effective display name (fallback to user ID if empty)
  String get effectiveDisplayName => displayName.isNotEmpty ? displayName : userId;

  @override
  String toString() {
    return 'ProfileEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'avatar: $avatar, '
        'cover: $cover, '
        'displayName: $displayName, '
        'originalAvatar: $originalAvatar, '
        'phoneNumber: $phoneNumber, '
        'email: $email, '
        'userBadgeType: $userBadgeType'
        '}';
  }
}
