import 'package:objectbox/objectbox.dart';

/// Visited profile entity for tracking visited user profiles
/// UID range: 2200-2204
@Entity()
class VisitedProfileEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 2200)
  String sessionKey = '';

  /// Visited user ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 2201)
  String visitedUserId = '';

  /// Whether the visit has been read (UID UPDATED)
  @Property(uid: 2202)
  bool isRead = false;

  /// Visit creation time (UID UPDATED)
  @Property(uid: 2203)
  DateTime? createTime;

  /// Visit last update time (UID UPDATED)
  @Property(uid: 2204)
  DateTime? updateTime;

  /// Default constructor
  VisitedProfileEntity();

  /// Constructor with required fields
  VisitedProfileEntity.create({
    required this.sessionKey,
    required this.visitedUserId,
    this.isRead = false,
    this.createTime,
    this.updateTime,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  VisitedProfileEntity copyWith({
    int? id,
    String? sessionKey,
    String? visitedUserId,
    bool? isRead,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return VisitedProfileEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..visitedUserId = visitedUserId ?? this.visitedUserId
      ..isRead = isRead ?? this.isRead
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Mark as read
  void markAsRead() {
    isRead = true;
    updateTime = DateTime.now();
  }

  /// Mark as unread
  void markAsUnread() {
    isRead = false;
    updateTime = DateTime.now();
  }

  /// Update visit time
  void updateVisitTime() {
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'VisitedProfileEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'visitedUserId: $visitedUserId, '
        'isRead: $isRead, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
