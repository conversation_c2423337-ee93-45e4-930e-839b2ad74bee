import 'package:objectbox/objectbox.dart';

/// User entity for storing user information
/// UID range: 2001-2013
@Entity()
class UserEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, NEW FIELD)
  @Index()
  @Property(uid: 2013)
  String sessionKey = '';

  /// Unique user ID (indexed)
  @Unique()
  @Index()
  @Property(uid: 2001)
  String userId = '';

  /// Username
  @Property(uid: 2002)
  String username = '';

  /// User creation time
  @Property(uid: 2003)
  DateTime? createTime;

  /// User last update time
  @Property(uid: 2004)
  DateTime? updateTime;

  /// User type (integer enum)
  @Property(uid: 2005)
  int userType = 0;

  /// User connect link
  @Property(uid: 2006)
  String userConnectLink = '';

  /// Media permission setting
  @Property(uid: 2007)
  int mediaPermissionSetting = 0;

  /// Global notification status
  @Property(uid: 2009)
  bool globalNotificationStatus = true;

  /// SIP credentials
  @Property(uid: 2010)
  String sipCredentials = '';

  /// SIP address
  @Property(uid: 2011)
  String sipAddress = '';

  /// Whether this is a partial user record
  @Property(uid: 2012)
  bool isPartial = false;

  /// Composite index field: sessionUserId
  String get sessionUserId => '${sessionKey}_$userId';

  /// Default constructor
  UserEntity();

  /// Constructor with required fields
  UserEntity.create({
    required this.sessionKey,
    required this.userId,
    this.username = '',
    this.createTime,
    this.updateTime,
    this.userType = 0,
    this.userConnectLink = '',
    this.mediaPermissionSetting = 0,
    this.globalNotificationStatus = true,
    this.sipCredentials = '',
    this.sipAddress = '',
    this.isPartial = false,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  UserEntity copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? username,
    DateTime? createTime,
    DateTime? updateTime,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
    bool? isPartial,
  }) {
    return UserEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..username = username ?? this.username
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..userType = userType ?? this.userType
      ..userConnectLink = userConnectLink ?? this.userConnectLink
      ..mediaPermissionSetting = mediaPermissionSetting ?? this.mediaPermissionSetting
      ..globalNotificationStatus = globalNotificationStatus ?? this.globalNotificationStatus
      ..sipCredentials = sipCredentials ?? this.sipCredentials
      ..sipAddress = sipAddress ?? this.sipAddress
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Update user information
  void updateInfo({
    String? username,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
  }) {
    if (username != null) this.username = username;
    if (userType != null) this.userType = userType;
    if (userConnectLink != null) this.userConnectLink = userConnectLink;
    if (mediaPermissionSetting != null) this.mediaPermissionSetting = mediaPermissionSetting;
    if (globalNotificationStatus != null) this.globalNotificationStatus = globalNotificationStatus;
    if (sipCredentials != null) this.sipCredentials = sipCredentials;
    if (sipAddress != null) this.sipAddress = sipAddress;
    updateTime = DateTime.now();
  }

  /// Mark as complete user record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'UserEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'username: $username, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'userType: $userType, '
        'userConnectLink: $userConnectLink, '
        'mediaPermissionSetting: $mediaPermissionSetting, '
        'globalNotificationStatus: $globalNotificationStatus, '
        'sipCredentials: $sipCredentials, '
        'sipAddress: $sipAddress, '
        'isPartial: $isPartial'
        '}';
  }
}
