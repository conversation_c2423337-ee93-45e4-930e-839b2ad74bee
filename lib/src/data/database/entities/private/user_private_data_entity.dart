import 'package:objectbox/objectbox.dart';

/// User private data entity for storing user-specific private data
/// UID range: 7100-7107
@Entity()
class UserPrivateDataEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7100)
  String sessionKey = '';

  /// User ID (indexed)
  @Index()
  @Property(uid: 7101)
  String userId = '';

  /// Alias name for the user
  @Property(uid: 7102)
  String aliasName = '';

  /// Version number
  @Property(uid: 7103)
  int version = 0;

  /// Source information
  @Property(uid: 7104)
  String source = '';

  /// DM ID
  @Property(uid: 7105)
  String dmId = '';

  /// Whether user is blocked
  @Property(uid: 7106)
  bool blocked = false;

  /// Notification status
  @Property(uid: 7107)
  bool notificationStatus = true;

  /// Default constructor
  UserPrivateDataEntity();

  /// Constructor with required fields
  UserPrivateDataEntity.create({
    required this.sessionKey,
    required this.userId,
    this.aliasName = '',
    this.version = 0,
    this.source = '',
    this.dmId = '',
    this.blocked = false,
    this.notificationStatus = true,
  });

  /// Copy constructor for updates
  UserPrivateDataEntity copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? aliasName,
    int? version,
    String? source,
    String? dmId,
    bool? blocked,
    bool? notificationStatus,
  }) {
    return UserPrivateDataEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..aliasName = aliasName ?? this.aliasName
      ..version = version ?? this.version
      ..source = source ?? this.source
      ..dmId = dmId ?? this.dmId
      ..blocked = blocked ?? this.blocked
      ..notificationStatus = notificationStatus ?? this.notificationStatus;
  }

  /// Block the user
  void blockUser() {
    blocked = true;
    version++;
  }

  /// Unblock the user
  void unblockUser() {
    blocked = false;
    version++;
  }

  /// Enable notifications
  void enableNotifications() {
    notificationStatus = true;
    version++;
  }

  /// Disable notifications
  void disableNotifications() {
    notificationStatus = false;
    version++;
  }

  /// Update alias name
  void updateAliasName(String newAliasName) {
    aliasName = newAliasName;
    version++;
  }

  /// Update DM ID
  void updateDmId(String newDmId) {
    dmId = newDmId;
    version++;
  }

  /// Check if user has alias
  bool get hasAlias => aliasName.isNotEmpty;

  /// Get effective display name (alias or user ID)
  String get effectiveDisplayName => hasAlias ? aliasName : userId;

  @override
  String toString() {
    return 'UserPrivateDataEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'aliasName: $aliasName, '
        'version: $version, '
        'source: $source, '
        'dmId: $dmId, '
        'blocked: $blocked, '
        'notificationStatus: $notificationStatus'
        '}';
  }
}
