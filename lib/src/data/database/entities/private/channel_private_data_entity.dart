import 'package:objectbox/objectbox.dart';

/// Channel private data entity for storing channel-specific private data
/// UID range: 7200-7207
@Entity()
class ChannelPrivateDataEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7200)
  String sessionKey = '';

  /// Channel ID (indexed)
  @Index()
  @Property(uid: 7201)
  String channelId = '';

  /// Version number
  @Property(uid: 7202)
  int version = 0;

  /// Unread message count
  @Property(uid: 7203)
  int unreadCount = 0;

  /// Last seen message ID
  @Property(uid: 7204)
  String lastSeenMessageId = '';

  /// Whether channel is pinned
  @Property(uid: 7205)
  bool pinned = false;

  /// Sort order
  @Property(uid: 7206)
  int sort = 0;

  /// Source information
  @Property(uid: 7207)
  String source = '';

  /// Default constructor
  ChannelPrivateDataEntity();

  /// Constructor with required fields
  ChannelPrivateDataEntity.create({
    required this.sessionKey,
    required this.channelId,
    this.version = 0,
    this.unreadCount = 0,
    this.lastSeenMessageId = '',
    this.pinned = false,
    this.sort = 0,
    this.source = '',
  });

  /// Copy constructor for updates
  ChannelPrivateDataEntity copyWith({
    int? id,
    String? sessionKey,
    String? channelId,
    int? version,
    int? unreadCount,
    String? lastSeenMessageId,
    bool? pinned,
    int? sort,
    String? source,
  }) {
    return ChannelPrivateDataEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..channelId = channelId ?? this.channelId
      ..version = version ?? this.version
      ..unreadCount = unreadCount ?? this.unreadCount
      ..lastSeenMessageId = lastSeenMessageId ?? this.lastSeenMessageId
      ..pinned = pinned ?? this.pinned
      ..sort = sort ?? this.sort
      ..source = source ?? this.source;
  }

  /// Pin the channel
  void pin() {
    pinned = true;
    version++;
  }

  /// Unpin the channel
  void unpin() {
    pinned = false;
    version++;
  }

  /// Mark messages as read
  void markAsRead(String messageId) {
    lastSeenMessageId = messageId;
    unreadCount = 0;
    version++;
  }

  /// Increment unread count
  void incrementUnreadCount() {
    unreadCount++;
    version++;
  }

  /// Set unread count
  void setUnreadCount(int count) {
    unreadCount = count;
    version++;
  }

  /// Update sort order
  void updateSort(int newSort) {
    sort = newSort;
    version++;
  }

  /// Reset unread count
  void resetUnreadCount() {
    unreadCount = 0;
    version++;
  }

  /// Check if channel has unread messages
  bool get hasUnreadMessages => unreadCount > 0;

  @override
  String toString() {
    return 'ChannelPrivateDataEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'channelId: $channelId, '
        'version: $version, '
        'unreadCount: $unreadCount, '
        'lastSeenMessageId: $lastSeenMessageId, '
        'pinned: $pinned, '
        'sort: $sort, '
        'source: $source'
        '}';
  }
}
