import 'package:objectbox/objectbox.dart';

/// Private data entity for storing private data
/// UID range: 7001-7004
@Entity()
class PrivateDataEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7001)
  String sessionKey = '';

  /// User ID (indexed)
  @Index()
  @Property(uid: 7002)
  String userId = '';

  /// Creation time
  @Property(uid: 7003)
  DateTime? createTime;

  /// Last update time
  @Property(uid: 7004)
  DateTime? updateTime;

  /// Default constructor
  PrivateDataEntity();

  /// Constructor with required fields
  PrivateDataEntity.create({
    required this.sessionKey,
    required this.userId,
    this.createTime,
    this.updateTime,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  PrivateDataEntity copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return PrivateDataEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Update the record
  void update() {
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'PrivateDataEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'createTime: $createTime, '
        'updateTime: $updateTime'
        '}';
  }
}
