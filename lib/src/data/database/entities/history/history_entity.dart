import 'package:objectbox/objectbox.dart';

/// History entity for storing history records
/// UID range: 6100-6106 (Changed to avoid conflict with <PERSON>)
@Entity()
class HistoryEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed, UID UPDATED)
  @Index()
  @Property(uid: 6100)
  String sessionKey = '';

  /// History ID (indexed, UID UPDATED)
  @Index()
  @Property(uid: 6101)
  String historyId = '';

  /// Entity type (UID UPDATED)
  @Property(uid: 6102)
  String entityType = '';

  /// Entity ID (UID UPDATED)
  @Property(uid: 6103)
  String entityId = '';

  /// Action performed (UID UPDATED)
  @Property(uid: 6104)
  String action = '';

  /// Change data (UID UPDATED)
  @Property(uid: 6105)
  String changeData = '';

  /// Creation time (UID UPDATED)
  @Property(uid: 6106)
  DateTime? createTime;

  /// Composite index field: sessionEntityType
  String get sessionEntityType => '${sessionKey}_$entityType';

  /// Composite index field: entityTypeAction
  String get entityTypeAction => '${entityType}_$action';

  /// Composite index field: sessionCreateTime
  String get sessionCreateTime => '${sessionKey}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  HistoryEntity();

  /// Constructor with required fields
  HistoryEntity.create({
    required this.sessionKey,
    required this.historyId,
    required this.entityType,
    required this.entityId,
    required this.action,
    this.changeData = '',
    this.createTime,
  }) {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  HistoryEntity copyWith({
    int? id,
    String? sessionKey,
    String? historyId,
    String? entityType,
    String? entityId,
    String? action,
    String? changeData,
    DateTime? createTime,
  }) {
    return HistoryEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..historyId = historyId ?? this.historyId
      ..entityType = entityType ?? this.entityType
      ..entityId = entityId ?? this.entityId
      ..action = action ?? this.action
      ..changeData = changeData ?? this.changeData
      ..createTime = createTime ?? this.createTime;
  }

  /// Check if history has change data
  bool get hasChangeData => changeData.isNotEmpty;

  /// Get action type
  String get actionType => action.toLowerCase();

  /// Check if action is create
  bool get isCreateAction => actionType == 'create';

  /// Check if action is update
  bool get isUpdateAction => actionType == 'update';

  /// Check if action is delete
  bool get isDeleteAction => actionType == 'delete';

  /// Check if action is read
  bool get isReadAction => actionType == 'read';

  @override
  String toString() {
    return 'HistoryEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'historyId: $historyId, '
        'entityType: $entityType, '
        'entityId: $entityId, '
        'action: $action, '
        'changeData: ${changeData.length > 50 ? changeData.substring(0, 50) + '...' : changeData}, '
        'createTime: $createTime'
        '}';
  }
}
