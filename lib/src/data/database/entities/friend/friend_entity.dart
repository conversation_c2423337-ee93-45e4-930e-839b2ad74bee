import 'package:objectbox/objectbox.dart';

/// Friend entity for storing friend relationships
/// UID range: 5001-5007
@Entity()
class FriendEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 5001)
  String sessionKey = '';

  /// Owner user ID (indexed)
  @Index()
  @Property(uid: 5002)
  String ownerUserId = '';

  /// Friend user ID (indexed)
  @Index()
  @Property(uid: 5003)
  String friendUserId = '';

  /// Friendship status
  @Property(uid: 5004)
  int status = 0;

  /// Status update time
  @Property(uid: 5005)
  DateTime? statusUpdateTime;

  /// Friendship creation time
  @Property(uid: 5006)
  DateTime? createTime;

  /// Whether this is a partial friend record
  @Property(uid: 5007)
  bool isPartial = false;

  /// Composite index field: ownerFriendStatus
  String get ownerFriendStatus => '${ownerUserId}_${friendUserId}_$status';

  /// Composite index field: sessionOwnerStatus
  String get sessionOwnerStatus => '${sessionKey}_${ownerUserId}_$status';

  /// Composite index field: sessionFriendStatus
  String get sessionFriendStatus => '${sessionKey}_${friendUserId}_$status';

  /// Default constructor
  FriendEntity();

  /// Constructor with required fields
  FriendEntity.create({
    required this.sessionKey,
    required this.ownerUserId,
    required this.friendUserId,
    this.status = 0,
    this.statusUpdateTime,
    this.createTime,
    this.isPartial = false,
  }) {
    this.statusUpdateTime ??= DateTime.now();
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  FriendEntity copyWith({
    int? id,
    String? sessionKey,
    String? ownerUserId,
    String? friendUserId,
    int? status,
    DateTime? statusUpdateTime,
    DateTime? createTime,
    bool? isPartial,
  }) {
    return FriendEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..ownerUserId = ownerUserId ?? this.ownerUserId
      ..friendUserId = friendUserId ?? this.friendUserId
      ..status = status ?? this.status
      ..statusUpdateTime = statusUpdateTime ?? this.statusUpdateTime
      ..createTime = createTime ?? this.createTime
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Update friendship status
  void updateStatus(int newStatus) {
    status = newStatus;
    statusUpdateTime = DateTime.now();
  }

  /// Mark as complete friend record
  void markAsComplete() {
    isPartial = false;
  }

  /// Check if friendship is active
  bool get isActive => status == 1; // Assuming 1 means active

  /// Check if friendship is pending
  bool get isPending => status == 0; // Assuming 0 means pending

  /// Check if friendship is blocked
  bool get isBlocked => status == -1; // Assuming -1 means blocked

  @override
  String toString() {
    return 'FriendEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'ownerUserId: $ownerUserId, '
        'friendUserId: $friendUserId, '
        'status: $status, '
        'statusUpdateTime: $statusUpdateTime, '
        'createTime: $createTime, '
        'isPartial: $isPartial'
        '}';
  }
}
