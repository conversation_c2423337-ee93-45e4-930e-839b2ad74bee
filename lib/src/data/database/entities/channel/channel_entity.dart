import 'package:objectbox/objectbox.dart';

/// Channel entity for storing communication channel information
/// UID range: 3001-3016
@Entity()
class ChannelEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed)
  @Index()
  @Property(uid: 3001)
  String workspaceId = '';

  /// Unique channel ID (indexed)
  @Unique()
  @Index()
  @Property(uid: 3002)
  String channelId = '';

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 3003)
  String sessionKey = '';

  /// Channel owner user ID (indexed, FIXED)
  @Index()
  @Property(uid: 3004)
  String channelOwnerUserId = '';

  /// DM channel ID
  @Property(uid: 3005)
  String dmChannelId = '';

  /// Recipient ID (indexed, FIXED)
  @Index()
  @Property(uid: 3006)
  String recipientId = '';

  /// Channel name
  @Property(uid: 3007)
  String name = '';

  /// Channel avatar URL
  @Property(uid: 3008)
  String avatar = '';

  /// Original avatar URL
  @Property(uid: 3009)
  String originalAvatar = '';

  /// Channel type (raw integer)
  @Property(uid: 3010)
  int channelTypeRaw = 0;

  /// Channel topic
  @Property(uid: 3011)
  String topic = '';

  /// Channel description
  @Property(uid: 3012)
  String description = '';

  /// Channel creation time
  @Property(uid: 3013)
  DateTime? createTime;

  /// Channel last update time
  @Property(uid: 3014)
  DateTime? updateTime;

  /// Whether channel is archived
  @Property(uid: 3015)
  bool isArchived = false;

  /// Whether this is a partial channel record
  @Property(uid: 3016)
  bool isPartial = false;

  /// Composite index field: sessionChannelId
  String get sessionChannelId => '${sessionKey}_$channelId';

  /// Composite index field: workspaceSessionChannel
  String get workspaceSessionChannel => '${workspaceId}_${sessionKey}_$channelId';

  /// Default constructor
  ChannelEntity();

  /// Constructor with required fields
  ChannelEntity.create({
    required this.workspaceId,
    required this.channelId,
    required this.sessionKey,
    required this.channelOwnerUserId,
    required this.recipientId,
    this.dmChannelId = '',
    this.name = '',
    this.avatar = '',
    this.originalAvatar = '',
    this.channelTypeRaw = 0,
    this.topic = '',
    this.description = '',
    this.createTime,
    this.updateTime,
    this.isArchived = false,
    this.isPartial = false,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  ChannelEntity copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey,
    String? channelOwnerUserId,
    String? dmChannelId,
    String? recipientId,
    String? name,
    String? avatar,
    String? originalAvatar,
    int? channelTypeRaw,
    String? topic,
    String? description,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isArchived,
    bool? isPartial,
  }) {
    return ChannelEntity()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..sessionKey = sessionKey ?? this.sessionKey
      ..channelOwnerUserId = channelOwnerUserId ?? this.channelOwnerUserId
      ..dmChannelId = dmChannelId ?? this.dmChannelId
      ..recipientId = recipientId ?? this.recipientId
      ..name = name ?? this.name
      ..avatar = avatar ?? this.avatar
      ..originalAvatar = originalAvatar ?? this.originalAvatar
      ..channelTypeRaw = channelTypeRaw ?? this.channelTypeRaw
      ..topic = topic ?? this.topic
      ..description = description ?? this.description
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isArchived = isArchived ?? this.isArchived
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Archive the channel
  void archive() {
    isArchived = true;
    updateTime = DateTime.now();
  }

  /// Unarchive the channel
  void unarchive() {
    isArchived = false;
    updateTime = DateTime.now();
  }

  /// Update channel information
  void updateInfo({
    String? name,
    String? avatar,
    String? originalAvatar,
    String? topic,
    String? description,
    int? channelTypeRaw,
  }) {
    if (name != null) this.name = name;
    if (avatar != null) this.avatar = avatar;
    if (originalAvatar != null) this.originalAvatar = originalAvatar;
    if (topic != null) this.topic = topic;
    if (description != null) this.description = description;
    if (channelTypeRaw != null) this.channelTypeRaw = channelTypeRaw;
    updateTime = DateTime.now();
  }

  /// Mark as complete channel record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Check if channel has avatar
  bool get hasAvatar => avatar.isNotEmpty;

  /// Check if channel is DM
  bool get isDM => dmChannelId.isNotEmpty;

  @override
  String toString() {
    return 'ChannelEntity{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'sessionKey: $sessionKey, '
        'channelOwnerUserId: $channelOwnerUserId, '
        'dmChannelId: $dmChannelId, '
        'recipientId: $recipientId, '
        'name: $name, '
        'avatar: $avatar, '
        'originalAvatar: $originalAvatar, '
        'channelTypeRaw: $channelTypeRaw, '
        'topic: $topic, '
        'description: $description, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isArchived: $isArchived, '
        'isPartial: $isPartial'
        '}';
  }
}
