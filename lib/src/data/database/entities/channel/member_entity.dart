import 'package:objectbox/objectbox.dart';

/// Member entity for storing channel member information
/// UID range: 3100-3109
@Entity()
class MemberEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Workspace ID (indexed)
  @Index()
  @Property(uid: 3100)
  String workspaceId = '';

  /// Channel ID (indexed)
  @Index()
  @Property(uid: 3101)
  String channelId = '';

  /// User ID (indexed)
  @Index()
  @Property(uid: 3102)
  String userId = '';

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 3103)
  String sessionKey = '';

  /// Member nickname in the channel
  @Property(uid: 3104)
  String nickname = '';

  /// Member role
  @Property(uid: 3105)
  String role = '';

  /// Raw roles data
  @Property(uid: 3106)
  String rolesRaw = '';

  /// Member creation time
  @Property(uid: 3107)
  DateTime? createTime;

  /// Member last update time
  @Property(uid: 3108)
  DateTime? updateTime;

  /// Whether this is a partial member record
  @Property(uid: 3109)
  bool isPartial = false;

  /// Composite index field: channelUserId
  String get channelUserId => '${channelId}_$userId';

  /// Composite index field: sessionChannelUser
  String get sessionChannelUser => '${sessionKey}_${channelId}_$userId';

  /// Default constructor
  MemberEntity();

  /// Constructor with required fields
  MemberEntity.create({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.sessionKey,
    this.nickname = '',
    this.role = '',
    this.rolesRaw = '',
    this.createTime,
    this.updateTime,
    this.isPartial = false,
  }) {
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  MemberEntity copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? sessionKey,
    String? nickname,
    String? role,
    String? rolesRaw,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isPartial,
  }) {
    return MemberEntity()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..userId = userId ?? this.userId
      ..sessionKey = sessionKey ?? this.sessionKey
      ..nickname = nickname ?? this.nickname
      ..role = role ?? this.role
      ..rolesRaw = rolesRaw ?? this.rolesRaw
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isPartial = isPartial ?? this.isPartial;
  }

  /// Update member information
  void updateInfo({
    String? nickname,
    String? role,
    String? rolesRaw,
  }) {
    if (nickname != null) this.nickname = nickname;
    if (role != null) this.role = role;
    if (rolesRaw != null) this.rolesRaw = rolesRaw;
    updateTime = DateTime.now();
  }

  /// Mark as complete member record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Check if member has nickname
  bool get hasNickname => nickname.isNotEmpty;

  /// Get effective display name (nickname or user ID)
  String get effectiveDisplayName => nickname.isNotEmpty ? nickname : userId;

  @override
  String toString() {
    return 'MemberEntity{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'userId: $userId, '
        'sessionKey: $sessionKey, '
        'nickname: $nickname, '
        'role: $role, '
        'rolesRaw: $rolesRaw, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isPartial: $isPartial'
        '}';
  }
}
