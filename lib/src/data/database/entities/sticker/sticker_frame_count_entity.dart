import 'package:objectbox/objectbox.dart';

/// Sticker frame count entity for storing sticker frame count information
/// UID range: 8200-8203
@Entity()
class StickerFrameCountEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 8200)
  String sessionKey = '';

  /// Sticker ID (indexed)
  @Index()
  @Property(uid: 8201)
  String stickerId = '';

  /// Frame count
  @Property(uid: 8202)
  int frameCount = 0;

  /// Update time
  @Property(uid: 8203)
  DateTime? updateTime;

  /// Default constructor
  StickerFrameCountEntity();

  /// Constructor with required fields
  StickerFrameCountEntity.create({
    required this.sessionKey,
    required this.stickerId,
    this.frameCount = 0,
    this.updateTime,
  }) {
    this.updateTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  StickerFrameCountEntity copyWith({
    int? id,
    String? sessionKey,
    String? stickerId,
    int? frameCount,
    DateTime? updateTime,
  }) {
    return StickerFrameCountEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..stickerId = stickerId ?? this.stickerId
      ..frameCount = frameCount ?? this.frameCount
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Update frame count
  void updateFrameCount(int newFrameCount) {
    frameCount = newFrameCount;
    updateTime = DateTime.now();
  }

  /// Increment frame count
  void incrementFrameCount() {
    frameCount++;
    updateTime = DateTime.now();
  }

  /// Decrement frame count
  void decrementFrameCount() {
    if (frameCount > 0) {
      frameCount--;
      updateTime = DateTime.now();
    }
  }

  /// Check if sticker is animated
  bool get isAnimated => frameCount > 1;

  /// Check if sticker is static
  bool get isStatic => frameCount <= 1;

  @override
  String toString() {
    return 'StickerFrameCountEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'stickerId: $stickerId, '
        'frameCount: $frameCount, '
        'updateTime: $updateTime'
        '}';
  }
}
