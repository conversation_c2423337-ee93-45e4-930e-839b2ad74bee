import 'package:objectbox/objectbox.dart';

/// Call log entity for storing call history
/// UID range: 9001-9014
@Entity()
class CallLogEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 9001)
  String sessionKey = '';

  /// Call ID (indexed)
  @Index()
  @Property(uid: 9002)
  String callId = '';

  /// Caller user ID (indexed, FIXED)
  @Index()
  @Property(uid: 9003)
  String callerId = '';

  /// Callee user ID (indexed, FIXED)
  @Index()
  @Property(uid: 9004)
  String calleeId = '';

  /// Call state
  @Property(uid: 9005)
  int callState = 0;

  /// Ended reason
  @Property(uid: 9006)
  int endedReason = 0;

  /// Call duration in seconds
  @Property(uid: 9007)
  int callTimeInSeconds = 0;

  /// Whether it's a missed call
  @Property(uid: 9008)
  bool isMissedCall = false;

  /// Whether it's an incoming call
  @Property(uid: 9009)
  bool isInComingCall = false;

  /// Whether it's an outgoing call
  @Property(uid: 9010)
  bool isOutgoing = false;

  /// Whether it's a video call
  @Property(uid: 9011)
  bool isVideoCall = false;

  /// Read time
  @Property(uid: 9012)
  DateTime? readTime;

  /// Call creation time
  @Property(uid: 9013)
  DateTime? createTime;

  /// Call ended time
  @Property(uid: 9014)
  DateTime? endedTime;

  /// Composite index field: callerCalleeTime
  String get callerCalleeTime => '${callerId}_${calleeId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Composite index field: sessionCallerTime
  String get sessionCallerTime => '${sessionKey}_${callerId}_${createTime?.millisecondsSinceEpoch ?? 0}';

  /// Default constructor
  CallLogEntity();

  /// Constructor with required fields
  CallLogEntity.create({
    required this.sessionKey,
    required this.callId,
    required this.callerId,
    required this.calleeId,
    this.callState = 0,
    this.endedReason = 0,
    this.callTimeInSeconds = 0,
    this.isMissedCall = false,
    this.isInComingCall = false,
    this.isOutgoing = false,
    this.isVideoCall = false,
    this.readTime,
    this.createTime,
    this.endedTime,
  }) {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  CallLogEntity copyWith({
    int? id,
    String? sessionKey,
    String? callId,
    String? callerId,
    String? calleeId,
    int? callState,
    int? endedReason,
    int? callTimeInSeconds,
    bool? isMissedCall,
    bool? isInComingCall,
    bool? isOutgoing,
    bool? isVideoCall,
    DateTime? readTime,
    DateTime? createTime,
    DateTime? endedTime,
  }) {
    return CallLogEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..callId = callId ?? this.callId
      ..callerId = callerId ?? this.callerId
      ..calleeId = calleeId ?? this.calleeId
      ..callState = callState ?? this.callState
      ..endedReason = endedReason ?? this.endedReason
      ..callTimeInSeconds = callTimeInSeconds ?? this.callTimeInSeconds
      ..isMissedCall = isMissedCall ?? this.isMissedCall
      ..isInComingCall = isInComingCall ?? this.isInComingCall
      ..isOutgoing = isOutgoing ?? this.isOutgoing
      ..isVideoCall = isVideoCall ?? this.isVideoCall
      ..readTime = readTime ?? this.readTime
      ..createTime = createTime ?? this.createTime
      ..endedTime = endedTime ?? this.endedTime;
  }

  /// End the call
  void endCall({int? endedReason, int? callTimeInSeconds}) {
    this.endedReason = endedReason ?? 0;
    this.callTimeInSeconds = callTimeInSeconds ?? 0;
    endedTime = DateTime.now();
  }

  /// Mark as read
  void markAsRead() {
    readTime = DateTime.now();
  }

  /// Get call duration as formatted string
  String get formattedDuration {
    final hours = callTimeInSeconds ~/ 3600;
    final minutes = (callTimeInSeconds % 3600) ~/ 60;
    final seconds = callTimeInSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Check if call is active
  bool get isActive => callState == 1; // Assuming 1 means active

  /// Check if call is ended
  bool get isEnded => endedTime != null;

  @override
  String toString() {
    return 'CallLogEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'callId: $callId, '
        'callerId: $callerId, '
        'calleeId: $calleeId, '
        'callState: $callState, '
        'endedReason: $endedReason, '
        'callTimeInSeconds: $callTimeInSeconds, '
        'isMissedCall: $isMissedCall, '
        'isInComingCall: $isInComingCall, '
        'isOutgoing: $isOutgoing, '
        'isVideoCall: $isVideoCall, '
        'createTime: $createTime, '
        'endedTime: $endedTime'
        '}';
  }
}
