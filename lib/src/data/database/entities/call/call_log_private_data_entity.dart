import 'package:objectbox/objectbox.dart';

/// Call log private data entity for storing call-specific private data
/// UID range: 7300-7310
@Entity()
class CallLogPrivateDataEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 7300)
  String sessionKey = '';

  /// Call ID (indexed)
  @Index()
  @Property(uid: 7301)
  String callId = '';

  /// Call state
  @Property(uid: 7302)
  int callState = 0;

  /// Ended reason
  @Property(uid: 7303)
  int endedReason = 0;

  /// Call duration in seconds
  @Property(uid: 7304)
  int callTimeInSeconds = 0;

  /// Read time
  @Property(uid: 7305)
  DateTime? readTime;

  /// Ended time
  @Property(uid: 7306)
  DateTime? endedTime;

  /// Source information
  @Property(uid: 7307)
  String source = '';

  /// Version number
  @Property(uid: 7308)
  int version = 0;

  /// Creation time
  @Property(uid: 7309)
  DateTime? createTime;

  /// Call type
  @Property(uid: 7310)
  int callType = 0;

  /// Default constructor
  CallLogPrivateDataEntity();

  /// Constructor with required fields
  CallLogPrivateDataEntity.create({
    required this.sessionKey,
    required this.callId,
    this.callState = 0,
    this.endedReason = 0,
    this.callTimeInSeconds = 0,
    this.readTime,
    this.endedTime,
    this.source = '',
    this.version = 0,
    this.createTime,
    this.callType = 0,
  }) {
    this.createTime ??= DateTime.now();
  }

  /// Copy constructor for updates
  CallLogPrivateDataEntity copyWith({
    int? id,
    String? sessionKey,
    String? callId,
    int? callState,
    int? endedReason,
    int? callTimeInSeconds,
    DateTime? readTime,
    DateTime? endedTime,
    String? source,
    int? version,
    DateTime? createTime,
    int? callType,
  }) {
    return CallLogPrivateDataEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..callId = callId ?? this.callId
      ..callState = callState ?? this.callState
      ..endedReason = endedReason ?? this.endedReason
      ..callTimeInSeconds = callTimeInSeconds ?? this.callTimeInSeconds
      ..readTime = readTime ?? this.readTime
      ..endedTime = endedTime ?? this.endedTime
      ..source = source ?? this.source
      ..version = version ?? this.version
      ..createTime = createTime ?? this.createTime
      ..callType = callType ?? this.callType;
  }

  /// Update call state
  void updateCallState(int newState) {
    callState = newState;
    version++;
  }

  /// End the call
  void endCall({int? endedReason, int? callTimeInSeconds}) {
    this.endedReason = endedReason ?? 0;
    this.callTimeInSeconds = callTimeInSeconds ?? 0;
    endedTime = DateTime.now();
    version++;
  }

  /// Mark as read
  void markAsRead() {
    readTime = DateTime.now();
    version++;
  }

  /// Update call type
  void updateCallType(int newType) {
    callType = newType;
    version++;
  }

  /// Get call duration as formatted string
  String get formattedDuration {
    final hours = callTimeInSeconds ~/ 3600;
    final minutes = (callTimeInSeconds % 3600) ~/ 60;
    final seconds = callTimeInSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Check if call is active
  bool get isActive => callState == 1; // Assuming 1 means active

  /// Check if call is ended
  bool get isEnded => endedTime != null;

  /// Check if call has been read
  bool get isRead => readTime != null;

  @override
  String toString() {
    return 'CallLogPrivateDataEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'callId: $callId, '
        'callState: $callState, '
        'endedReason: $endedReason, '
        'callTimeInSeconds: $callTimeInSeconds, '
        'readTime: $readTime, '
        'endedTime: $endedTime, '
        'source: $source, '
        'version: $version, '
        'createTime: $createTime, '
        'callType: $callType'
        '}';
  }
}
