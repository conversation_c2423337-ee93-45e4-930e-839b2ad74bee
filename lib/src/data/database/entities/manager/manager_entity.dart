import 'package:objectbox/objectbox.dart';

/// Manager entity for storing manager settings
/// UID range: 5100-5106
@Entity()
class ManagerEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 5100)
  String sessionKey = '';

  /// Whether all channels have been loaded
  @Property(uid: 5101)
  bool loadedAllChannels = false;

  /// Whether all friends have been loaded
  @Property(uid: 5102)
  bool loadedAllFriends = false;

  /// Whether all friend requests have been loaded
  @Property(uid: 5103)
  bool loadedAllFriendRequests = false;

  /// Whether message request warning has been closed
  @Property(uid: 5104)
  bool closedMessageRequestWarning = false;

  /// Whether list block user warning has been closed
  @Property(uid: 5105)
  bool closedListBlockUserWarning = false;

  /// User status emojis data
  @Property(uid: 5106, type: PropertyType.string)
  String userStatusEmojis = '';

  /// Default constructor
  ManagerEntity();

  /// Constructor with required fields
  ManagerEntity.create({
    required this.sessionKey,
    this.loadedAllChannels = false,
    this.loadedAllFriends = false,
    this.loadedAllFriendRequests = false,
    this.closedMessageRequestWarning = false,
    this.closedListBlockUserWarning = false,
    this.userStatusEmojis = '',
  });

  /// Copy constructor for updates
  ManagerEntity copyWith({
    int? id,
    String? sessionKey,
    bool? loadedAllChannels,
    bool? loadedAllFriends,
    bool? loadedAllFriendRequests,
    bool? closedMessageRequestWarning,
    bool? closedListBlockUserWarning,
    String? userStatusEmojis,
  }) {
    return ManagerEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..loadedAllChannels = loadedAllChannels ?? this.loadedAllChannels
      ..loadedAllFriends = loadedAllFriends ?? this.loadedAllFriends
      ..loadedAllFriendRequests = loadedAllFriendRequests ?? this.loadedAllFriendRequests
      ..closedMessageRequestWarning = closedMessageRequestWarning ?? this.closedMessageRequestWarning
      ..closedListBlockUserWarning = closedListBlockUserWarning ?? this.closedListBlockUserWarning
      ..userStatusEmojis = userStatusEmojis ?? this.userStatusEmojis;
  }

  /// Mark all channels as loaded
  void markAllChannelsLoaded() {
    loadedAllChannels = true;
  }

  /// Mark all friends as loaded
  void markAllFriendsLoaded() {
    loadedAllFriends = true;
  }

  /// Mark all friend requests as loaded
  void markAllFriendRequestsLoaded() {
    loadedAllFriendRequests = true;
  }

  /// Close message request warning
  void closeMessageRequestWarning() {
    closedMessageRequestWarning = true;
  }

  /// Close list block user warning
  void closeListBlockUserWarning() {
    closedListBlockUserWarning = true;
  }

  /// Update user status emojis
  void updateUserStatusEmojis(String emojis) {
    userStatusEmojis = emojis;
  }

  /// Reset all loading states
  void resetLoadingStates() {
    loadedAllChannels = false;
    loadedAllFriends = false;
    loadedAllFriendRequests = false;
  }

  /// Check if all data has been loaded
  bool get isAllDataLoaded => loadedAllChannels && loadedAllFriends && loadedAllFriendRequests;

  @override
  String toString() {
    return 'ManagerEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'loadedAllChannels: $loadedAllChannels, '
        'loadedAllFriends: $loadedAllFriends, '
        'loadedAllFriendRequests: $loadedAllFriendRequests, '
        'closedMessageRequestWarning: $closedMessageRequestWarning, '
        'closedListBlockUserWarning: $closedListBlockUserWarning, '
        'userStatusEmojis: $userStatusEmojis'
        '}';
  }
}
