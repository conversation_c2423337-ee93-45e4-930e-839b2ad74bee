import 'package:objectbox/objectbox.dart';

/// Session local metadata entity for storing session-specific settings
/// UID range: 1100-1109
@Entity()
class SessionLocalMetadataEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Session key reference (indexed)
  @Index()
  @Property(uid: 1100)
  String sessionKey = '';

  /// Whether intro channel view has been shown
  @Property(uid: 1101)
  bool introChannelView = false;

  /// Whether intro translate entire chat has been shown
  @Property(uid: 1102)
  bool introTranslateEntireChat = false;

  /// Whether intro channel list has been shown
  @Property(uid: 1103)
  bool introChannelList = false;

  /// Whether intro call log has been shown
  @Property(uid: 1104)
  bool introCallLog = false;

  /// Whether intro create channel has been shown
  @Property(uid: 1105)
  bool introCreateChannel = false;

  /// Speech language setting
  @Property(uid: 1106)
  String speechLang = '';

  /// Resume ID for session restoration
  @Property(uid: 1107)
  String resumeId = '';

  /// Whether speech to text is enabled
  @Property(uid: 1108)
  bool speechToTextEnable = false;

  /// User update time after (FIXED field)
  @Property(uid: 1109)
  DateTime? userUpdateTimeAfter;

  /// Default constructor
  SessionLocalMetadataEntity();

  /// Constructor with required fields
  SessionLocalMetadataEntity.create({
    required this.sessionKey,
    this.introChannelView = false,
    this.introTranslateEntireChat = false,
    this.introChannelList = false,
    this.introCallLog = false,
    this.introCreateChannel = false,
    this.speechLang = '',
    this.resumeId = '',
    this.speechToTextEnable = false,
    this.userUpdateTimeAfter,
  });

  /// Copy constructor for updates
  SessionLocalMetadataEntity copyWith({
    int? id,
    String? sessionKey,
    bool? introChannelView,
    bool? introTranslateEntireChat,
    bool? introChannelList,
    bool? introCallLog,
    bool? introCreateChannel,
    String? speechLang,
    String? resumeId,
    bool? speechToTextEnable,
    DateTime? userUpdateTimeAfter,
  }) {
    return SessionLocalMetadataEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..introChannelView = introChannelView ?? this.introChannelView
      ..introTranslateEntireChat = introTranslateEntireChat ?? this.introTranslateEntireChat
      ..introChannelList = introChannelList ?? this.introChannelList
      ..introCallLog = introCallLog ?? this.introCallLog
      ..introCreateChannel = introCreateChannel ?? this.introCreateChannel
      ..speechLang = speechLang ?? this.speechLang
      ..resumeId = resumeId ?? this.resumeId
      ..speechToTextEnable = speechToTextEnable ?? this.speechToTextEnable
      ..userUpdateTimeAfter = userUpdateTimeAfter ?? this.userUpdateTimeAfter;
  }

  /// Mark all intro screens as viewed
  void markAllIntrosViewed() {
    introChannelView = true;
    introTranslateEntireChat = true;
    introChannelList = true;
    introCallLog = true;
    introCreateChannel = true;
  }

  /// Reset all intro screens
  void resetIntros() {
    introChannelView = false;
    introTranslateEntireChat = false;
    introChannelList = false;
    introCallLog = false;
    introCreateChannel = false;
  }

  @override
  String toString() {
    return 'SessionLocalMetadataEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'introChannelView: $introChannelView, '
        'introTranslateEntireChat: $introTranslateEntireChat, '
        'introChannelList: $introChannelList, '
        'introCallLog: $introCallLog, '
        'introCreateChannel: $introCreateChannel, '
        'speechLang: $speechLang, '
        'resumeId: $resumeId, '
        'speechToTextEnable: $speechToTextEnable, '
        'userUpdateTimeAfter: $userUpdateTimeAfter'
        '}';
  }
}
