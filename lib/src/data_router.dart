/// {@template data_router}
/// Local-first data router with Clean Architecture
/// {@endtemplate}

// Core exports
export 'core/database.dart';
export 'core/config/config.dart';
export 'core/di/di.dart';

// Database entities
export 'data/database/entities/entities.dart';

// Shared types
export 'shared/types/result.dart';

/// Main DataRouter class
class DataRouter {
  /// {@macro data_router}
  const DataRouter();
}
