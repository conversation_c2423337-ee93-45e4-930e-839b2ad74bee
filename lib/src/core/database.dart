import 'dart:io';

import 'package:flutter/foundation.dart';

import '../data/database/entities/session/session_entity.dart';
import '../data/database/generated/objectbox.g.dart';
import '../shared/types/result.dart';

/// Main database class for DataRouter
/// Manages ObjectBox store and provides database operations
class DataRouterDatabase {
  final Store _store;
  Admin? _admin;

  /// Constructor for dependency injection
  DataRouterDatabase(this._store) {
    _initializeAdmin();
  }

  /// Alternative constructor with store injection
  DataRouterDatabase.withStore(this._store) {
    _initializeAdmin();
  }

  /// Get the ObjectBox store instance
  Store get store => _store;

  /// Check if database is initialized
  bool get isInitialized => true; // Always true since store is injected

  /// Get the ObjectBox Admin instance (only available in debug mode)
  Admin? get admin => _admin;

  /// Initialize admin user session for development
  /// This is called automatically when the database is created
  Future<Result<void>> initializeAdminUser() async {
    try {
      await ();
      return const Success(null);
    } catch (e, stackTrace) {
      return Failure(
        'Failed to initialize admin user: ${e.toString()}',
        Exception(
          'Admin user initialization error: $e\nStack trace: $stackTrace',
        ),
      );
    }
  }

  /// Close the database admin (store is managed by DI container)
  Future<Result<void>> closeAdmin() async {
    try {
      // Close admin if it exists
      _admin?.close();
      _admin = null;
      return const Success(null);
    } catch (e) {
      return Failure(
        'Failed to close admin: ${e.toString()}',
        Exception('Admin close error: $e'),
      );
    }
  }

  /// Clear all data from database (for development/testing)
  Future<Result<void>> clearAllData() async {
    try {
      // Clear session box
      final sessionBox = _store.box<SessionEntity>();
      sessionBox.removeAll();

      return const Success(null);
    } catch (e) {
      return Failure(
        'Failed to clear database: ${e.toString()}',
        Exception('Database clear error: $e'),
      );
    }
  }

  /// Get database statistics
  Future<Result<Map<String, dynamic>>> getStats() async {
    try {
      final stats = <String, dynamic>{};

      // Get session count
      final sessionBox = _store.box<SessionEntity>();
      stats['sessions'] = {
        'total': sessionBox.count(),
        'active': sessionBox
            .query(SessionEntity_.isActive.equals(true))
            .build()
            .count(),
      };

      // Add database info
      stats['database'] = {
        'path': _store.directoryPath,
        'size': await _getDatabaseSize(),
      };

      return Success(stats);
    } catch (e) {
      return Failure(
        'Failed to get database stats: ${e.toString()}',
        Exception('Database stats error: $e'),
      );
    }
  }

  /// Initialize ObjectBox Admin for debug builds
  void _initializeAdmin() {
    if (kDebugMode && Admin.isAvailable()) {
      try {
        _admin = Admin(store, bindUri: 'http://127.0.0.1:8090');
        print('✅ ObjectBox Admin started at http://127.0.0.1:8090');
      } catch (e) {
        print('❌ Failed to start ObjectBox Admin: $e');
      }
    }
  }

  /// Get database size in bytes
  Future<int> _getDatabaseSize() async {
    try {
      final directory = Directory(store.directoryPath);
      if (!directory.existsSync()) return 0;

      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Get formatted database size
  String getFormattedDatabaseSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
