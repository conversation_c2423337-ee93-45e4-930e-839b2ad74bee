import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';

import '../../data/database/generated/objectbox.g.dart';
import '../database.dart';

/// Database dependency injection module
/// Provides database-related dependencies
@module
abstract class DatabaseModule {
  /// Provide ObjectBox Store instance
  /// This is a singleton that will be shared across the app
  @LazySingleton()
  @preResolve
  Future<Store> provideObjectBoxStore() async {
    // Get application documents directory
    final docsDir = await getApplicationDocumentsDirectory();
    final storeDir = Directory('${docsDir.path}/data_router_db');

    // Create directory if it doesn't exist
    if (!storeDir.existsSync()) {
      storeDir.createSync(recursive: true);
    }

    // Open ObjectBox store
    return Store(
      getObjectBoxModel(),
      directory: storeDir.path,
    );
  }

  /// Provide DataRouterDatabase instance
  /// This depends on the Store instance
  @LazySingleton()
  DataRouterDatabase provideDataRouterDatabase(Store store) {
    return DataRouterDatabase(store);
  }
}
