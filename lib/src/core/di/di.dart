import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../database.dart';
import 'di.config.dart';

/// Global GetIt instance for dependency injection
final GetIt getIt = GetIt.instance;

/// Configure dependency injection
/// This should be called before using any dependencies
@InjectableInit()
Future<void> configureInjection() async {
  await getIt.init();
}

/// Reset dependency injection (useful for testing)
Future<void> resetInjection() async {
  await getIt.reset();
}

/// Check if dependency injection is configured
bool get isInjectionConfigured => getIt.isRegistered<DataRouterDatabase>();
