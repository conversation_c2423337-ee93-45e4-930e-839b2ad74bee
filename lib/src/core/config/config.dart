import 'package:injectable/injectable.dart';
import '../di/di.dart';

/// Configuration class for DataRouter package
/// Manages initialization and configuration of the package
@LazySingleton()
class DataRouterConfig {
  bool _isInitialized = false;
  
  /// Check if the package is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the DataRouter package
  /// This should be called before using any DataRouter functionality
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    // Configure dependency injection
    await configureInjection();
    
    // Mark as initialized
    _isInitialized = true;
  }

  /// Reset the configuration (useful for testing)
  Future<void> reset() async {
    await resetInjection();
    _isInitialized = false;
  }

  /// Get singleton instance
  static DataRouterConfig get instance => getIt<DataRouterConfig>();
}
