import 'package:data_router/data_router.dart';
import 'package:flutter/material.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await configureInjection();

  runApp(const DataRouterExampleApp());
}

/// Example Flutter app demonstrating DataRouter package usage
class DataRouterExampleApp extends StatelessWidget {
  const DataRouterExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DataRouter Example',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const DataRouterHomePage(),
    );
  }
}

/// Main home page demonstrating all DataRouter functionality
/// This serves as the primary entry point for all current and future features
class DataRouterHomePage extends StatefulWidget {
  const DataRouterHomePage({super.key});

  @override
  State<DataRouterHomePage> createState() => _DataRouterHomePageState();
}

class _DataRouterHomePageState extends State<DataRouterHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // Initialize tab controller for future feature tabs
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('DataRouter Demo'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.storage), text: 'Database'),
            // Future tabs can be added here:
            // Tab(icon: Icon(Icons.api), text: 'API'),
            // Tab(icon: Icon(Icons.wifi), text: 'WebSocket'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          DatabaseDemoTab(),
          // Future feature tabs can be added here:
          // ApiDemoTab(),
          // WebSocketDemoTab(),
        ],
      ),
    );
  }
}

// ============================================================================
// DATABASE DEMO TAB
// ============================================================================

/// Database demonstration tab showing ObjectBox functionality
/// Includes database initialization, ObjectBox Admin integration, and CRUD operations
class DatabaseDemoTab extends StatefulWidget {
  const DatabaseDemoTab({super.key});

  @override
  State<DatabaseDemoTab> createState() => _DatabaseDemoTabState();
}

class _DatabaseDemoTabState extends State<DatabaseDemoTab> {
  // Database instance from DI
  late final Database _database;

  // State variables
  bool _isInitialized = false;
  bool _isLoading = false;
  String _statusMessage = 'Database not initialized';
  Map<String, dynamic>? _stats;
  List<SessionEntity> _sessions = [];

  @override
  void initState() {
    super.initState();
    // Get database instance from dependency injection
    _database = getIt<Database>();
    _initializeDatabase();
  }

  @override
  void dispose() {
    _database.closeAdmin();
    super.dispose();
  }

  // ============================================================================
  // DATABASE OPERATIONS
  // ============================================================================

  /// Initialize the database demo (database is already initialized through DI)
  Future<void> _initializeDatabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Loading database...';
    });

    try {
      // Initialize admin user if needed
      await _database.initializeAdminUser();

      setState(() {
        _isInitialized = true;
        _isLoading = false;
        _statusMessage = 'Database loaded successfully!';
      });

      _loadStats();
      _loadSessions();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Failed to load database: $e';
      });
    }
  }

  /// Load database statistics
  Future<void> _loadStats() async {
    if (!_isInitialized) return;

    final result = await _database.getStats();
    result.fold(
      onSuccess: (stats) {
        setState(() {
          _stats = stats;
        });
      },
      onFailure: (message, exception) {
        setState(() {
          _statusMessage = 'Failed to load stats: $message';
        });
      },
    );
  }

  /// Load all sessions from database
  Future<void> _loadSessions() async {
    if (!_isInitialized) return;

    try {
      final sessionBox = _database.store.box<SessionEntity>();
      final sessions = sessionBox.getAll();
      setState(() {
        _sessions = sessions;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to load sessions: $e';
      });
    }
  }

  /// Create a test session for demonstration
  Future<void> _createTestSession() async {
    if (!_isInitialized) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Creating test session...';
    });

    try {
      final sessionBox = _database.store.box<SessionEntity>();
      final testSession = SessionEntity.create(
        sessionKey: 'test_session_${DateTime.now().millisecondsSinceEpoch}',
        sessionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        sessionToken: 'token_${DateTime.now().millisecondsSinceEpoch}',
      );

      sessionBox.put(testSession);

      setState(() {
        _isLoading = false;
        _statusMessage = 'Test session created successfully!';
      });

      _loadStats();
      _loadSessions();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Failed to create test session: $e';
      });
    }
  }

  /// Clear all data from database
  Future<void> _clearAllData() async {
    if (!_isInitialized) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Clearing all data...';
    });

    final result = await _database.clearAllData();

    result.fold(
      onSuccess: (_) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'All data cleared successfully!';
        });
        _loadStats();
        _loadSessions();
      },
      onFailure: (message, exception) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Failed to clear data: $message';
        });
      },
    );
  }

  // ============================================================================
  // UI BUILD METHOD
  // ============================================================================

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Database Status Card
          _buildStatusCard(),
          const SizedBox(height: 16),

          // Database Statistics Card
          if (_stats != null) ...[
            _buildStatsCard(),
            const SizedBox(height: 16),
          ],

          // Action Buttons
          _buildActionButtons(),
          const SizedBox(height: 16),

          // Sessions List
          _buildSessionsList(),
        ],
      ),
    );
  }

  // ============================================================================
  // UI HELPER METHODS
  // ============================================================================

  /// Build database status card
  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.error,
                  color: _isInitialized ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Database Status',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(_statusMessage),
            if (_isLoading) ...[
              const SizedBox(height: 8),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build database statistics card with ObjectBox Admin info
  Widget _buildStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Database Statistics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Total Sessions: ${_stats!['sessions']['total']}'),
            Text('Active Sessions: ${_stats!['sessions']['active']}'),
            Text('Database Path: ${_stats!['database']['path']}'),
            Text(
              'Database Size: ${_database.getFormattedDatabaseSize(_stats!['database']['size'])}',
            ),
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _database.admin != null
                      ? Icons.admin_panel_settings
                      : Icons.admin_panel_settings_outlined,
                  color: _database.admin != null ? Colors.blue : Colors.grey,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'ObjectBox Admin: ${_database.admin != null ? "Running at http://127.0.0.1:8090" : "Not available"}',
                    style: TextStyle(
                      color:
                          _database.admin != null ? Colors.blue : Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build action buttons for database operations
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed:
                _isInitialized && !_isLoading ? _createTestSession : null,
            icon: const Icon(Icons.add),
            label: const Text('Create Test Session'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isInitialized && !_isLoading ? _clearAllData : null,
            icon: const Icon(Icons.clear),
            label: const Text('Clear All Data'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// Build sessions list widget
  Widget _buildSessionsList() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sessions',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child:
                _sessions.isEmpty
                    ? const Center(child: Text('No sessions found'))
                    : ListView.builder(
                      itemCount: _sessions.length,
                      itemBuilder: (context, index) {
                        final session = _sessions[index];
                        return Card(
                          child: ListTile(
                            leading: Icon(
                              session.isActive
                                  ? Icons.circle
                                  : Icons.circle_outlined,
                              color:
                                  session.isActive ? Colors.green : Colors.grey,
                            ),
                            title: Text('User: ${session.userId}'),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Session: ${session.sessionId}'),
                                Text('Device: ${session.deviceInfo}'),
                                Text(
                                  'Login: ${session.loginTime.toString().split('.')[0]}',
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}

// ============================================================================
// FUTURE FEATURE TABS (PLACEHOLDER STRUCTURE)
// ============================================================================

// When implementing future features, add new tab classes here following this pattern:
//
// /// API demonstration tab showing HTTP client functionality
// class ApiDemoTab extends StatefulWidget {
//   const ApiDemoTab({super.key});
//   @override
//   State<ApiDemoTab> createState() => _ApiDemoTabState();
// }
//
// class _ApiDemoTabState extends State<ApiDemoTab> {
//   // API demo implementation
//   @override
//   Widget build(BuildContext context) {
//     return const Center(child: Text('API Demo - Coming Soon'));
//   }
// }
//
// /// WebSocket demonstration tab showing real-time communication
// class WebSocketDemoTab extends StatefulWidget {
//   const WebSocketDemoTab({super.key});
//   @override
//   State<WebSocketDemoTab> createState() => _WebSocketDemoTabState();
// }
//
// class _WebSocketDemoTabState extends State<WebSocketDemoTab> {
//   // WebSocket demo implementation
//   @override
//   Widget build(BuildContext context) {
//     return const Center(child: Text('WebSocket Demo - Coming Soon'));
//   }
// }
